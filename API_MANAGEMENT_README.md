# API Management Feature

## Overview

The API Management feature provides a comprehensive interface for managing API configurations in the CTI Dashboard. Users can add, edit, and delete API configurations for various threat intelligence services through a secure web interface.

## Features

### ✅ Implemented Features

1. **Dynamic API Service Management**
   - Add custom API services beyond the default ones
   - Remove custom services (default services are protected)
   - Categorize services (AI, Threat Intelligence, Search Engines, Custom)

2. **Secure API Key Handling**
   - Input validation and sanitization
   - Secure storage (environment variables/encrypted storage)
   - No hardcoded API keys in source files
   - Rate limiting for API operations

3. **User-Friendly Interface**
   - Dark theme support with professional styling
   - Tabbed interface organized by service categories
   - Individual service cards with configuration options
   - Visual status indicators (configured/not configured)

4. **Enhanced User Experience**
   - Loading states and progress indicators
   - Success/error notifications with animations
   - Confirmation dialogs for destructive actions
   - Form validation with real-time feedback
   - Responsive design for mobile devices

5. **Security Features**
   - Input sanitization to prevent XSS attacks
   - API key validation with service-specific rules
   - Rate limiting to prevent abuse
   - Secure error handling without information leakage

## Architecture

### Backend Components

1. **Enhanced Data Models** (`backend/app/api/v1/endpoints/system.py`)
   - `APIKeyRequest`: API key configuration with validation
   - `APIServiceRequest`: Custom service creation with validation
   - `APIKeyResponse`: Secure response without exposing keys

2. **API Key Manager** (`backend/app/core/security.py`)
   - Dynamic service configuration
   - Encrypted key storage
   - Service metadata management
   - Default service definitions

3. **API Endpoints**
   - `GET /system/services`: List all available services
   - `POST /system/services`: Add custom service
   - `DELETE /system/services/{service}`: Remove custom service
   - `POST /system/api-keys`: Set API key for service
   - `DELETE /system/api-keys/{service}`: Remove API key
   - `GET /system/api-keys`: List configured services

### Frontend Components

1. **Enhanced Settings Interface** (`frontend/js/app.js`)
   - Dynamic service loading from backend
   - Service card rendering with category organization
   - Modal dialogs for adding custom services
   - Real-time validation and feedback

2. **Styling and Themes** (`frontend/css/`)
   - Enhanced API service card styles
   - Dark theme support with professional colors
   - Responsive design patterns
   - Accessibility improvements

## Usage Guide

### Adding a Custom Service

1. Navigate to Settings → API Configuration
2. Click "Add Custom Service" button
3. Fill in the service details:
   - **Service Name**: Unique identifier (letters, numbers, underscores, hyphens only)
   - **Display Label**: User-friendly name
   - **Description**: Optional description
   - **Category**: Service category
   - **Requires Secret**: Check if service needs both ID and secret
4. Click "Add Service"

### Configuring API Keys

1. Find the service card in the appropriate category tab
2. Enter the API key (and secret if required)
3. Click "Save" to store the configuration
4. Use "Test" to verify the connection
5. Use the trash icon to clear the key if needed

### Managing Services

- **View All Services**: Use the "All Services" tab
- **Filter by Category**: Use category tabs (AI Services, Threat Intelligence, etc.)
- **Remove Custom Services**: Click the trash icon in the service card header
- **Test Connections**: Use "Test All Connections" for bulk testing

## Security Considerations

### Input Validation

- Service names: Alphanumeric, underscores, hyphens only (max 50 chars)
- Labels: Max 100 characters with HTML entity encoding
- Descriptions: Max 500 characters with sanitization
- API keys: Max 1000 characters with XSS prevention

### API Key Security

- Keys are encrypted before storage
- No keys are exposed in API responses
- Local storage used as fallback for client-side compatibility
- Rate limiting prevents brute force attacks

### Error Handling

- Sanitized error messages prevent information leakage
- Graceful degradation when backend is unavailable
- User-friendly error notifications

## Testing

### Frontend Testing

Run the test page to validate functionality:

```bash
# Open in browser
open test_api_management.html
```

Test features:
- Service loading and rendering
- Input validation
- Notification system
- Confirmation dialogs
- Security features

### Backend Testing

Run the Python test script:

```bash
# Install dependencies
pip install requests

# Run tests
python test_backend_api.py

# Test against different URL
python test_backend_api.py --url http://localhost:8000
```

Test coverage:
- Health check
- Service listing
- Custom service creation
- Input validation
- API key management
- Security features

## Configuration

### Environment Variables

Set these environment variables for secure API key storage:

```bash
# Example API keys (use your actual keys)
export CYFIRMA_API_KEY="your-cyfirma-key"
export VIRUSTOTAL_API_KEY="your-virustotal-key"
export SHODAN_API_KEY="your-shodan-key"
export CENSYS_API_ID="your-censys-id"
export CENSYS_API_SECRET="your-censys-secret"
export ZOOMEYE_API_KEY="your-zoomeye-key"
export ABUSEIPDB_API_KEY="your-abuseipdb-key"
export OPENAI_API_KEY="your-openai-key"
export DEEPSEEK_API_KEY="your-deepseek-key"
```

### Default Services

The system includes these default services:

- **AI Services**: Gemini, DeepSeek, OpenAI
- **Threat Intelligence**: Cyfirma, VirusTotal, AbuseIPDB
- **Search Engines**: Shodan, Censys, ZoomEye

## Troubleshooting

### Common Issues

1. **Services not loading**
   - Check backend connectivity
   - Verify API endpoints are accessible
   - Check browser console for errors

2. **API keys not saving**
   - Verify input validation passes
   - Check network connectivity
   - Ensure proper permissions

3. **Custom services not appearing**
   - Refresh the page
   - Check if service was actually created
   - Verify service name uniqueness

### Debug Mode

Enable debug logging in browser console:

```javascript
// Enable debug mode
dashboard.debugMode = true;

// Check service status
console.log(dashboard.apiServices);
console.log(dashboard.apiKeys);
```

## Future Enhancements

- [ ] Bulk import/export of API configurations
- [ ] API key rotation and expiration management
- [ ] Service health monitoring and alerts
- [ ] Integration with external secret management systems
- [ ] Advanced service templates and presets
- [ ] API usage analytics and reporting

## Support

For issues or questions:

1. Check the troubleshooting section
2. Run the test suite to identify problems
3. Review browser console for error messages
4. Check backend logs for API errors
