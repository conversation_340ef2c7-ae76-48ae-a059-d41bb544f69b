"""
Watchlist Monitoring Database Models
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy import Column, Integer, String, Float, DateTime, JSON, Text, Boolean
from sqlalchemy.orm import relationship
from pydantic import BaseModel, validator
from enum import Enum

from .base import BaseDBModel, BaseSchema, BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema


class WatchlistItemType(str, Enum):
    """Watchlist item types"""
    IP = "ip"
    DOMAIN = "domain"
    URL = "url"
    EMAIL = "email"
    HASH = "hash"
    SUBNET = "subnet"
    KEYWORD = "keyword"
    ACTOR = "actor"
    MALWARE = "malware"


class AlertSeverity(str, Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    """Alert status"""
    OPEN = "open"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    FALSE_POSITIVE = "false_positive"


class WatchlistItem(BaseDBModel):
    """Watchlist Item database model"""
    
    __tablename__ = "watchlist_items"
    
    # Core item fields
    value = Column(String(2048), nullable=False, index=True)
    item_type = Column(String(50), nullable=False, index=True)
    description = Column(Text, nullable=False)
    
    # Classification
    tags = Column(JSON, default=list, nullable=False)
    severity = Column(String(20), default="medium", nullable=False)
    confidence = Column(Float, default=0.8, nullable=False)
    
    # Matching configuration
    match_type = Column(String(50), default="exact", nullable=False)  # exact, partial, regex, subnet
    case_sensitive = Column(Boolean, default=False, nullable=False)
    
    # Temporal information
    expiry_date = Column(DateTime, nullable=True)
    last_matched = Column(DateTime, nullable=True)
    match_count = Column(Integer, default=0, nullable=False)
    
    # Source information
    source = Column(String(255), nullable=True)
    external_reference = Column(String(500), nullable=True)
    
    # Audit fields
    added_by = Column(String(255), nullable=True)
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Metadata
    metadata = Column(JSON, default=dict, nullable=False)
    
    def __repr__(self):
        return f"<WatchlistItem(id={self.id}, value='{self.value}', type='{self.item_type}')>"


class WatchlistAlert(BaseDBModel):
    """Watchlist Alert database model"""
    
    __tablename__ = "watchlist_alerts"
    
    # Core alert fields
    watchlist_item_id = Column(Integer, nullable=False, index=True)  # Foreign key
    matched_value = Column(String(2048), nullable=False, index=True)
    match_type = Column(String(50), nullable=False)
    
    # Alert details
    source = Column(String(255), nullable=False, index=True)
    source_data = Column(JSON, nullable=True)
    context = Column(JSON, default=dict, nullable=False)
    
    # Classification
    severity = Column(String(20), nullable=False, index=True)
    confidence = Column(Float, default=0.8, nullable=False)
    
    # Status tracking
    status = Column(String(50), default="open", nullable=False, index=True)
    acknowledged = Column(Boolean, default=False, nullable=False)
    acknowledged_by = Column(String(255), nullable=True)
    acknowledged_at = Column(DateTime, nullable=True)
    
    # Resolution
    resolved_by = Column(String(255), nullable=True)
    resolved_at = Column(DateTime, nullable=True)
    resolution_notes = Column(Text, nullable=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    tags = Column(JSON, default=list, nullable=False)
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    def __repr__(self):
        return f"<WatchlistAlert(id={self.id}, matched='{self.matched_value}', severity='{self.severity}')>"


class WatchlistRule(BaseDBModel):
    """Watchlist Rule database model for complex matching logic"""
    
    __tablename__ = "watchlist_rules"
    
    # Rule identification
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Rule logic
    rule_type = Column(String(50), nullable=False)  # simple, regex, composite
    conditions = Column(JSON, nullable=False)  # Rule conditions
    actions = Column(JSON, nullable=False)  # Actions to take on match
    
    # Configuration
    enabled = Column(Boolean, default=True, nullable=False)
    priority = Column(Integer, default=5, nullable=False)  # 1-10, higher = more priority
    
    # Performance tracking
    match_count = Column(Integer, default=0, nullable=False)
    last_matched = Column(DateTime, nullable=True)
    execution_time_ms = Column(Float, default=0.0, nullable=False)
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)


# Pydantic schemas for API
class WatchlistItemBase(BaseSchema):
    """Base watchlist item schema"""
    value: str
    item_type: WatchlistItemType
    description: str
    tags: List[str] = []
    severity: AlertSeverity = AlertSeverity.MEDIUM
    confidence: float = 0.8
    match_type: str = "exact"
    case_sensitive: bool = False
    source: Optional[str] = None
    external_reference: Optional[str] = None
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Confidence must be between 0.0 and 1.0')
        return v
    
    @validator('match_type')
    def validate_match_type(cls, v):
        valid_types = ['exact', 'partial', 'regex', 'subnet']
        if v not in valid_types:
            raise ValueError(f'Match type must be one of: {", ".join(valid_types)}')
        return v


class WatchlistItemCreate(WatchlistItemBase, BaseCreateSchema):
    """Schema for creating watchlist items"""
    expiry_days: Optional[int] = None


class WatchlistItemUpdate(BaseUpdateSchema):
    """Schema for updating watchlist items"""
    value: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    severity: Optional[AlertSeverity] = None
    confidence: Optional[float] = None
    match_type: Optional[str] = None
    case_sensitive: Optional[bool] = None
    expiry_date: Optional[datetime] = None


class WatchlistItemResponse(WatchlistItemBase, BaseResponseSchema):
    """Schema for watchlist item responses"""
    expiry_date: Optional[datetime] = None
    last_matched: Optional[datetime] = None
    match_count: int = 0
    added_by: Optional[str] = None
    created_by: Optional[str] = None
    is_active: bool = True
    metadata: Dict[str, Any] = {}


class WatchlistAlertBase(BaseSchema):
    """Base watchlist alert schema"""
    watchlist_item_id: int
    matched_value: str
    match_type: str
    source: str
    severity: AlertSeverity
    confidence: float = 0.8
    status: AlertStatus = AlertStatus.OPEN
    notes: Optional[str] = None
    tags: List[str] = []


class WatchlistAlertCreate(WatchlistAlertBase, BaseCreateSchema):
    """Schema for creating watchlist alerts"""
    source_data: Optional[Dict[str, Any]] = None
    context: Dict[str, Any] = {}


class WatchlistAlertUpdate(BaseUpdateSchema):
    """Schema for updating watchlist alerts"""
    status: Optional[AlertStatus] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None
    acknowledged: Optional[bool] = None
    acknowledged_by: Optional[str] = None
    resolved_by: Optional[str] = None
    resolution_notes: Optional[str] = None


class WatchlistAlertResponse(WatchlistAlertBase, BaseResponseSchema):
    """Schema for watchlist alert responses"""
    source_data: Optional[Dict[str, Any]] = None
    context: Dict[str, Any] = {}
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved_by: Optional[str] = None
    resolved_at: Optional[datetime] = None
    resolution_notes: Optional[str] = None
    created_by: Optional[str] = None
    is_active: bool = True


class WatchlistRuleBase(BaseSchema):
    """Base watchlist rule schema"""
    name: str
    description: Optional[str] = None
    rule_type: str
    conditions: Dict[str, Any]
    actions: Dict[str, Any]
    enabled: bool = True
    priority: int = 5
    
    @validator('priority')
    def validate_priority(cls, v):
        if not 1 <= v <= 10:
            raise ValueError('Priority must be between 1 and 10')
        return v
    
    @validator('rule_type')
    def validate_rule_type(cls, v):
        valid_types = ['simple', 'regex', 'composite']
        if v not in valid_types:
            raise ValueError(f'Rule type must be one of: {", ".join(valid_types)}')
        return v


class WatchlistRuleCreate(WatchlistRuleBase, BaseCreateSchema):
    """Schema for creating watchlist rules"""
    pass


class WatchlistRuleUpdate(BaseUpdateSchema):
    """Schema for updating watchlist rules"""
    name: Optional[str] = None
    description: Optional[str] = None
    conditions: Optional[Dict[str, Any]] = None
    actions: Optional[Dict[str, Any]] = None
    enabled: Optional[bool] = None
    priority: Optional[int] = None


class WatchlistRuleResponse(WatchlistRuleBase, BaseResponseSchema):
    """Schema for watchlist rule responses"""
    match_count: int = 0
    last_matched: Optional[datetime] = None
    execution_time_ms: float = 0.0
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    is_active: bool = True


class WatchlistStatistics(BaseModel):
    """Watchlist statistics schema"""
    total_items: int
    active_items: int
    expired_items: int
    total_alerts: int
    open_alerts: int
    acknowledged_alerts: int
    resolved_alerts: int
    alerts_last_24h: int
    alerts_last_7d: int
    top_alert_sources: List[Dict[str, Any]]
    items_by_type: Dict[str, int]
    items_by_severity: Dict[str, int]
    alerts_by_severity: Dict[str, int]
    most_matched_items: List[Dict[str, Any]]
    average_response_time: Optional[float] = None  # In hours


class WatchlistMatchResult(BaseModel):
    """Result of watchlist matching operation"""
    matched: bool
    watchlist_item_id: Optional[int] = None
    match_type: Optional[str] = None
    confidence: Optional[float] = None
    context: Dict[str, Any] = {}
    alert_generated: bool = False
    alert_id: Optional[int] = None
