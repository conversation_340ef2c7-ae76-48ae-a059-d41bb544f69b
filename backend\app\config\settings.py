"""
Configuration management for CTI Dashboard
"""

import os
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    app_name: str = "CTI Dashboard"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    
    # API Configuration
    api_host: str = "127.0.0.1"
    api_port: int = 8000
    api_prefix: str = "/api/v1"
    
    # Security
    secret_key: str = "your-secret-key-here-change-in-production-minimum-32-characters"
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    
    # Database
    database_url: Optional[str] = None
    redis_url: Optional[str] = None
    
    # External API Keys (configured via UI or environment)
    cyfirma_api_key: Optional[str] = None
    virustotal_api_key: Optional[str] = None
    shodan_api_key: Optional[str] = None
    censys_api_id: Optional[str] = None
    censys_api_secret: Optional[str] = None
    zoomeye_api_key: Optional[str] = None
    abuseipdb_api_key: Optional[str] = None
    
    # LLM Configuration
    llm_service: str = "openai"  # openai, deepseek, ollama
    openai_api_key: Optional[str] = None
    deepseek_api_key: Optional[str] = None
    ollama_base_url: str = "http://localhost:11434"
    
    # Feature Flags
    enable_passive_scanning: bool = True
    enable_watchlist_monitoring: bool = True
    enable_ai_analysis: bool = True
    enable_subnet_matching: bool = True
    
    # Rate Limiting
    rate_limit_per_minute: int = 100
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    
    # CORS
    allowed_origins: str = "http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080"
    
    @validator("secret_key")
    def validate_secret_key(cls, v):
        if not v:
            raise ValueError("SECRET_KEY must be set")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @validator("environment")
    def validate_environment(cls, v):
        if v not in ["development", "staging", "production"]:
            raise ValueError("Environment must be development, staging, or production")
        return v
    
    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "case_sensitive": False, "extra": "ignore"}


class SecuritySettings(BaseSettings):
    """Security-specific settings"""

    # Authentication
    require_authentication: bool = True
    session_timeout_minutes: int = 480  # 8 hours
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15

    # API Security
    enable_rate_limiting: bool = True
    enable_request_logging: bool = True
    enable_cors: bool = True

    # Data Protection
    encrypt_sensitive_data: bool = True
    data_retention_days: int = 365

    model_config = {"env_prefix": "SECURITY_", "env_file": ".env"}


class IntegrationSettings(BaseSettings):
    """External integration settings"""

    # API Timeouts (seconds)
    default_timeout: int = 30
    cyfirma_timeout: int = 45
    virustotal_timeout: int = 30
    shodan_timeout: int = 30

    # Retry Configuration
    max_retries: int = 3
    retry_delay: float = 1.0

    # Cache Configuration
    cache_ttl_hours: int = 6
    enable_caching: bool = True

    # Batch Processing
    max_batch_size: int = 100
    batch_timeout: int = 300

    model_config = {"env_prefix": "INTEGRATION_", "env_file": ".env"}


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()


@lru_cache()
def get_security_settings() -> SecuritySettings:
    """Get cached security settings"""
    return SecuritySettings()


@lru_cache()
def get_integration_settings() -> IntegrationSettings:
    """Get cached integration settings"""
    return IntegrationSettings()


# Environment-specific configurations
def get_database_url() -> str:
    """Get database URL based on environment"""
    settings = get_settings()
    
    if settings.database_url:
        return settings.database_url
    
    # Default database URLs by environment
    if settings.environment == "production":
        return os.getenv("DATABASE_URL", "postgresql://user:pass@localhost/cti_prod")
    elif settings.environment == "staging":
        return os.getenv("DATABASE_URL", "postgresql://user:pass@localhost/cti_staging")
    else:
        return os.getenv("DATABASE_URL", "sqlite:///./cti_dev.db")


def get_redis_url() -> Optional[str]:
    """Get Redis URL for caching and sessions"""
    settings = get_settings()
    
    if settings.redis_url:
        return settings.redis_url
    
    if settings.environment == "production":
        return os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    return None  # Use in-memory cache for development


# Configuration validation
def validate_configuration():
    """Validate all configuration settings"""
    try:
        settings = get_settings()
        security_settings = get_security_settings()
        integration_settings = get_integration_settings()
        
        # Validate required settings for production
        if settings.environment == "production":
            required_keys = ["secret_key", "database_url"]
            missing_keys = [key for key in required_keys if not getattr(settings, key)]
            
            if missing_keys:
                raise ValueError(f"Missing required production settings: {missing_keys}")
        
        return True
        
    except Exception as e:
        raise ValueError(f"Configuration validation failed: {e}")
