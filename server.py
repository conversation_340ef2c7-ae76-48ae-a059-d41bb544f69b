#!/usr/bin/env python3
"""
CTI Dashboard Backend Server - Main Entry Point
This is the single main entry point for the CTI Dashboard backend server.
"""

import sys
import os
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "src" / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Also add the original backend directory for compatibility
original_backend_dir = Path(__file__).parent / "backend"
if str(original_backend_dir) not in sys.path:
    sys.path.insert(0, str(original_backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main entry point for the CTI Dashboard backend server"""
    
    print("🛡️ CTI Dashboard Backend Server")
    print("=" * 50)
    print("🚀 Starting Cyber Threat Intelligence Dashboard API...")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("⚠️  Press Ctrl+C to stop")
    print()
    
    try:
        # Try to import from the new structure first
        try:
            from app.main import app
            logger.info("✅ Loaded app from new structure")
        except ImportError:
            # Fallback to original structure
            logger.info("⚠️  Falling back to original structure")
            from backend.app.main import app
        
        # Import uvicorn
        import uvicorn
        
        # Get configuration
        host = os.getenv('API_HOST', '127.0.0.1')
        port = int(os.getenv('API_PORT', '8000'))
        debug = os.getenv('DEBUG', 'false').lower() == 'true'
        log_level = os.getenv('LOG_LEVEL', 'info').lower()
        
        logger.info(f"🌐 Server configuration:")
        logger.info(f"   Host: {host}")
        logger.info(f"   Port: {port}")
        logger.info(f"   Debug: {debug}")
        logger.info(f"   Log Level: {log_level}")
        
        # Run the server
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level=log_level,
            reload=debug,
            access_log=True
        )
        
    except ImportError as e:
        logger.error(f"❌ Failed to import application: {e}")
        logger.error("🔍 Make sure all dependencies are installed:")
        logger.error("   pip install -r requirements.txt")
        sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("\n👋 CTI Dashboard Backend stopped by user")
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
