#!/usr/bin/env python3
"""
CTI Dashboard Server Runner
This script properly runs the CTI Dashboard with full module imports enabled.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Now import and run the app
if __name__ == "__main__":
    try:
        # Import the app module
        from app.core.app import app
        import uvicorn
        
        print("🛡️ Starting CTI Dashboard with full Cyfirma integration...")
        print("📚 API Documentation: http://localhost:8000/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("⚠️  Press Ctrl+C to stop")
        
        # Run the server
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info",
            reload=False
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("🔄 Falling back to script mode...")
        
        # Fallback to script mode
        from app.core.app import app
        import uvicorn
        
        uvicorn.run(
            app,
            host="127.0.0.1", 
            port=8000,
            log_level="info",
            reload=False
        )
